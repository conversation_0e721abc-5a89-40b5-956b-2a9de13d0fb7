"""
System Route Registry for managing system routes dynamically
"""
import logging
from typing import Set, Dict, Any, Optional
from threading import Lock

logger = logging.getLogger(__name__)


class SystemRouteRegistry:
    """Registry for managing system routes that don't require database middleware"""
    
    def __init__(self):
        self._routes: Set[str] = set()
        self._route_prefixes: Set[str] = set()
        self._lock = Lock()
        self._initialized = False
        
        # Initialize with essential system routes that are always present
        self._initialize_core_routes()
    
    def _initialize_core_routes(self):
        """Initialize with core system routes that are always present"""
        # Core FastAPI routes
        core_routes = {
            "/docs", "/openapi.json", "/redoc", "/favicon.ico"
        }
        
        # Core route prefixes
        core_prefixes = {
            "/static/", "/docs", "/redoc"
        }
        
        with self._lock:
            self._routes.update(core_routes)
            self._route_prefixes.update(core_prefixes)
    
    def register_route(self, path: str, **kwargs) -> None:
        """
        Register a system route
        
        Args:
            path: Route path to register as system route
            **kwargs: Additional route metadata (ignored for system routes)
        """
        with self._lock:
            self._routes.add(path)
            logger.debug(f"Registered system route: {path}")
    
    def register_route_prefix(self, prefix: str) -> None:
        """
        Register a system route prefix
        
        Args:
            prefix: Route prefix to register (e.g., "/api/system/")
        """
        with self._lock:
            if not prefix.endswith('/'):
                prefix += '/'
            self._route_prefixes.add(prefix)
            logger.debug(f"Registered system route prefix: {prefix}")
    
    def is_system_route(self, path: str) -> bool:
        """
        Check if a path is a system route
        
        Args:
            path: URL path to check
            
        Returns:
            bool: True if path is a system route, False otherwise
        """
        with self._lock:
            # Check exact matches first
            if path in self._routes:
                return True
            
            # Check path prefixes
            for prefix in self._route_prefixes:
                if path.startswith(prefix):
                    return True
            
            return False
    
    def get_system_routes(self) -> Set[str]:
        """Get all registered system routes"""
        with self._lock:
            return self._routes.copy()
    
    def get_system_route_prefixes(self) -> Set[str]:
        """Get all registered system route prefixes"""
        with self._lock:
            return self._route_prefixes.copy()
    
    def register_fastapi_router_routes(self, router, prefix: str = "") -> None:
        """
        Register routes from a FastAPI router as system routes
        
        Args:
            router: FastAPI APIRouter instance
            prefix: Optional prefix to add to routes
        """
        if not hasattr(router, 'routes'):
            logger.warning(f"Router has no routes attribute: {router}")
            return
            
        with self._lock:
            for route in router.routes:
                if hasattr(route, 'path'):
                    full_path = prefix + route.path if prefix else route.path
                    self._routes.add(full_path)
                    logger.debug(f"Registered FastAPI router route as system route: {full_path}")
    
    def clear(self) -> None:
        """Clear all registered routes (except core routes)"""
        with self._lock:
            self._routes.clear()
            self._route_prefixes.clear()
            self._initialize_core_routes()
            logger.debug("Cleared system route registry")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get registry statistics"""
        with self._lock:
            return {
                'total_routes': len(self._routes),
                'total_prefixes': len(self._route_prefixes),
                'initialized': self._initialized
            }


# Global system route registry instance
_system_route_registry = SystemRouteRegistry()


def get_system_route_registry() -> SystemRouteRegistry:
    """Get the global system route registry instance"""
    return _system_route_registry


def register_system_route(path: str, **kwargs) -> None:
    """
    Convenience function to register a system route
    
    Args:
        path: Route path to register
        **kwargs: Additional route metadata
    """
    _system_route_registry.register_route(path, **kwargs)


def register_system_route_prefix(prefix: str) -> None:
    """
    Convenience function to register a system route prefix
    
    Args:
        prefix: Route prefix to register
    """
    _system_route_registry.register_route_prefix(prefix)


def is_system_route(path: str) -> bool:
    """
    Convenience function to check if a path is a system route
    
    Args:
        path: URL path to check
        
    Returns:
        bool: True if path is a system route, False otherwise
    """
    return _system_route_registry.is_system_route(path)
