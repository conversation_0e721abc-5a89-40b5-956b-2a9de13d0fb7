<?xml version="1.0" encoding="utf-8"?>
<templates id="template_database_list_enhanced" xml:space="preserve">

    <!-- Enhanced Database List Page Template -->
    <t t-name="database_list_enhanced.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <!-- Tailwind CSS CDN -->
                <script src="https://cdn.tailwindcss.com"></script>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
                <script>
                    tailwind.config = {
                        theme: {
                            extend: {
                                fontFamily: {
                                    'sans': ['Inter', 'system-ui', 'sans-serif'],
                                },
                                colors: {
                                    'primary': {
                                        50: '#eff6ff',
                                        500: '#3b82f6',
                                        600: '#2563eb',
                                        700: '#1d4ed8',
                                    }
                                }
                            }
                        }
                    }
                </script>

            </head>
            <body class="bg-gray-50 font-sans min-h-screen">
                <!-- App Header -->
                <header class="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
                    <div class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center text-white text-xl">
                                <i class="fas fa-database"></i>
                            </div>
                            <div>
                                <h1 class="text-xl font-bold text-gray-900">ERP System</h1>
                                <div class="text-sm text-gray-600">Enterprise Resource Planning</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" onclick="refreshDatabases()">
                                <i class="fas fa-sync-alt mr-2"></i>
                                Refresh
                            </button>
                            <button class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" onclick="showCreateDatabaseModal()">
                                <i class="fas fa-plus mr-2"></i>
                                Create Database
                            </button>
                        </div>
                    </div>
                </header>

                <!-- Main Content -->
                <main class="max-w-7xl mx-auto px-6 py-8">
                    <!-- Page Header -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">Database Selection</h1>
                        <p class="text-lg text-gray-600">Choose a database to access your ERP system and manage your business operations.</p>
                    </div>

                    <!-- Enhanced Status Bar -->
                    <div class="bg-white border border-gray-200 rounded-xl p-6 mb-8 shadow-sm">
                        <!-- Top Row: System Status and Search -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <div t-att-class="'inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold shadow-sm ' + ('bg-green-100 text-green-800 border border-green-200' if config.is_multi_db_mode else 'bg-yellow-100 text-yellow-800 border border-yellow-200')">
                                    <i t-att-class="'fas mr-2 ' + ('fa-layer-group' if config.is_multi_db_mode else 'fa-database')"></i>
                                    <span t-esc="'Multi-Database Mode' if config.is_multi_db_mode else 'Single-Database Mode'"/>
                                </div>
                                <t t-if="config.db_filter">
                                    <div class="inline-flex items-center px-3 py-2 bg-blue-50 text-blue-800 rounded-lg text-sm font-medium border border-blue-200">
                                        <i class="fas fa-filter mr-2"></i>
                                        <span>Filter: </span>
                                        <code class="ml-1 bg-blue-100 px-2 py-1 rounded text-xs" t-esc="config.db_filter"/>
                                    </div>
                                </t>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                    <input type="text" class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="Search databases..."
                                           id="database-search" onkeyup="filterDatabases()"/>
                                </div>
                            </div>
                        </div>

                        <!-- Bottom Row: Database Statistics -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-100">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-database text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900" t-esc="len(databases)"/>
                                    <div class="text-sm text-gray-500">Total Databases</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-check-circle text-green-600"></i>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900" t-esc="len([db for db in databases if db['init_status'] == 'ready'])"/>
                                    <div class="text-sm text-gray-500">Ready</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-memory text-purple-600"></i>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900" t-esc="len([db for db in databases if db['has_memory_registry']])"/>
                                    <div class="text-sm text-gray-500">With Registry</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-plug text-orange-600"></i>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900" t-esc="sum(db['active_connections'] for db in databases)"/>
                                    <div class="text-sm text-gray-500">Active Connections</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database List -->
                    <div id="database-list">
                        <t t-if="databases and len(databases) > 0">
                            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mb-8">
                                <t t-foreach="databases" t-as="db">
                                    <div class="group relative bg-white border border-gray-200 rounded-2xl overflow-hidden cursor-pointer transition-all duration-500 hover:shadow-2xl hover:scale-[1.02] hover:border-transparent" onclick="connectToDatabase(this.dataset.dbname)" t-att-data-dbname="db['name']">
                                        <!-- Gradient overlay on hover -->
                                        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/0 via-purple-50/0 to-indigo-50/0 group-hover:from-blue-50/30 group-hover:via-purple-50/20 group-hover:to-indigo-50/30 transition-all duration-500 pointer-events-none"></div>

                                        <!-- Top accent gradient -->
                                        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                                        <!-- Card content -->
                                        <div class="relative p-6">
                                            <!-- Database Header -->
                                            <div class="flex items-start justify-between mb-6">
                                                <div class="flex items-center space-x-4 flex-1 min-w-0">
                                                    <!-- Enhanced database icon with gradient -->
                                                    <div class="relative">
                                                        <div class="w-14 h-14 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                                                            <i class="fas fa-database"></i>
                                                        </div>
                                                        <!-- Status indicator dot -->
                                                        <div t-att-class="'absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white shadow-sm ' + ('bg-green-500' if db['init_status'] == 'ready' else 'bg-yellow-500')">
                                                            <div t-att-class="'w-full h-full rounded-full animate-ping ' + ('bg-green-400' if db['init_status'] == 'ready' else 'bg-yellow-400')"></div>
                                                        </div>
                                                    </div>

                                                    <div class="flex-1 min-w-0">
                                                        <h3 class="text-xl font-bold text-gray-900 truncate mb-1 group-hover:text-blue-700 transition-colors duration-300" t-esc="db['name']"/>
                                                        <p class="text-sm text-gray-500 truncate" t-esc="db['encoding'] + ' • ' + db['owner']"/>

                                                        <!-- Status badge -->
                                                        <div class="mt-2">
                                                            <span t-att-class="'inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm ' + ('bg-green-100 text-green-800 border border-green-200' if db['init_status'] == 'ready' else 'bg-yellow-100 text-yellow-800 border border-yellow-200')" t-att-title="db['init_message']">
                                                                <i t-att-class="'fas mr-1.5 ' + db['init_icon']"></i>
                                                                <span t-esc="'Ready' if db['init_status'] == 'ready' else 'Setup Required'"/>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Delete Button (Dev Mode Only) -->
                                                <t t-if="is_development_mode">
                                                    <button class="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 opacity-0 group-hover:opacity-100"
                                                            onclick="event.stopPropagation(); deleteDatabase(this.dataset.dbname)"
                                                            t-att-data-dbname="db['name']"
                                                            title="Delete Database (Dev Mode)">
                                                        <i class="fas fa-trash text-sm"></i>
                                                    </button>
                                                </t>
                                            </div>

                                            <!-- Enhanced Statistics Grid -->
                                            <div class="grid grid-cols-2 gap-3 mb-6">
                                                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200/50 group-hover:from-blue-100 group-hover:to-blue-200 transition-all duration-300">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-2">
                                                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                                                <i class="fas fa-hdd"></i>
                                                            </div>
                                                            <span class="text-xs font-semibold text-blue-700">Size</span>
                                                        </div>
                                                        <span class="text-sm font-bold text-blue-900" t-esc="db['size']"/>
                                                    </div>
                                                </div>

                                                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200/50 group-hover:from-green-100 group-hover:to-green-200 transition-all duration-300">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-2">
                                                            <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                                                <i class="fas fa-table"></i>
                                                            </div>
                                                            <span class="text-xs font-semibold text-green-700">Tables</span>
                                                        </div>
                                                        <span class="text-sm font-bold text-green-900" t-esc="db['table_count']"/>
                                                    </div>
                                                </div>

                                                <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200/50 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-2">
                                                            <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                                                <i class="fas fa-plug"></i>
                                                            </div>
                                                            <span class="text-xs font-semibold text-purple-700">Connections</span>
                                                        </div>
                                                        <span class="text-sm font-bold text-purple-900" t-esc="db['active_connections']"/>
                                                    </div>
                                                </div>

                                                <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200/50 group-hover:from-orange-100 group-hover:to-orange-200 transition-all duration-300">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-2">
                                                            <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                                                <i class="fas fa-calendar"></i>
                                                            </div>
                                                            <span class="text-xs font-semibold text-orange-700">Created</span>
                                                        </div>
                                                        <span class="text-xs font-bold text-orange-900" t-esc="db['created_display'] or 'Unknown'"/>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Enhanced Registry Status -->
                                            <t t-if="db['has_memory_registry']">
                                                <div class="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-xl">
                                                    <div class="flex items-center space-x-3">
                                                        <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center text-white text-xs shadow-sm">
                                                            <i class="fas fa-memory"></i>
                                                        </div>
                                                        <span class="text-sm font-semibold text-indigo-800">Memory Registry Active</span>
                                                        <div class="flex-1"></div>
                                                        <div class="flex space-x-1">
                                                            <div class="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
                                                            <div class="w-2 h-2 bg-indigo-400 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                                                            <div class="w-2 h-2 bg-indigo-300 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </t>

                                            <!-- Enhanced Action Button -->
                                            <t t-if="db['init_status'] == 'ready'">
                                                <button class="w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl text-sm font-semibold hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl group-hover:scale-[1.02]">
                                                    <i class="fas fa-rocket text-lg"></i>
                                                    <span>Connect to Database</span>
                                                    <i class="fas fa-arrow-right text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                                                </button>
                                            </t>
                                            <t t-else="">
                                                <button class="w-full px-6 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl text-sm font-semibold hover:from-yellow-600 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl group-hover:scale-[1.02]" onclick="initializeDatabase(this.dataset.dbname)" t-att-data-dbname="db['name']">
                                                    <i class="fas fa-cog text-lg"></i>
                                                    <span>Initialize Database</span>
                                                    <i class="fas fa-arrow-right text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                                                </button>
                                            </t>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </t>

                        <!-- Enhanced Empty State - Only shown when no databases exist -->
                        <t t-if="not databases or len(databases) == 0">
                            <div class="text-center py-24 bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 border-2 border-dashed border-gray-300 rounded-3xl shadow-inner">
                                <div class="relative mx-auto mb-8">
                                    <!-- Animated background circles -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="w-32 h-32 bg-blue-100 rounded-full animate-pulse opacity-20"></div>
                                    </div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="w-24 h-24 bg-indigo-200 rounded-full animate-ping opacity-10"></div>
                                    </div>

                                    <!-- Main icon -->
                                    <div class="relative w-28 h-28 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto text-white text-4xl shadow-2xl">
                                        <i class="fas fa-database"></i>
                                    </div>
                                </div>

                                <h3 class="text-3xl font-bold text-gray-900 mb-4">No databases found</h3>
                                <p class="text-lg text-gray-600 mb-10 max-w-2xl mx-auto leading-relaxed">
                                    Welcome to your ERP system! No databases are currently available.
                                    <br class="hidden sm:block"/>
                                    Create your first database to begin managing your business operations and unlock the full potential of your enterprise system.
                                </p>

                                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                                    <button class="inline-flex items-center px-8 py-4 rounded-xl text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105" onclick="showCreateDatabaseModal()">
                                        <i class="fas fa-plus mr-3 text-xl"></i>
                                        Create Your First Database
                                        <i class="fas fa-arrow-right ml-3"></i>
                                    </button>

                                    <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-gray-700 bg-white border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300 shadow-lg hover:shadow-xl" onclick="refreshDatabases()">
                                        <i class="fas fa-sync-alt mr-2"></i>
                                        Refresh List
                                    </button>
                                </div>
                            </div>
                        </t>
                    </div>

                    <!-- Footer -->
                    <footer class="text-center py-8 text-gray-500 text-sm border-t border-gray-200 mt-12">
                        <p>ERP System v1.0 | Powered by FastAPI &amp; PostgreSQL | <i class="fas fa-heart text-red-500"></i> Built with modern web technologies</p>
                    </footer>
                </main>

                <!-- Create Database Modal -->
                <div id="createDatabaseModal" class="modal fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
                    <div class="bg-white rounded-xl p-8 w-full max-w-md mx-4 shadow-2xl border border-gray-200">
                        <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                            <div class="flex items-center space-x-2 text-xl font-semibold text-gray-900">
                                <i class="fas fa-plus-circle text-blue-600"></i>
                                <span>Create New Database</span>
                            </div>
                            <button type="button" class="p-2 hover:bg-gray-100 rounded-md transition-colors text-gray-400 hover:text-gray-600" onclick="hideCreateDatabaseModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <form id="createDatabaseForm" onsubmit="handleCreateDatabase(event)">
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="dbName">Database Name *</label>
                                <input type="text" id="dbName" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                       placeholder="Enter database name" required="required"
                                       pattern="[a-zA-Z][a-zA-Z0-9_]*"
                                       title="Database name must start with a letter and contain only letters, numbers, and underscores"/>
                            </div>
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="dbLanguage">Default Language</label>
                                <select id="dbLanguage" name="language" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                    <option value="en_US">English (US)</option>
                                    <option value="en_GB">English (UK)</option>
                                    <option value="fr_FR">French</option>
                                    <option value="de_DE">German</option>
                                    <option value="es_ES">Spanish</option>
                                </select>
                            </div>
                            <div class="mb-6">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="loadDemo" name="demo" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"/>
                                    <span class="text-sm text-gray-700">Install demo data</span>
                                </label>
                            </div>
                            <div class="flex space-x-3 pt-6 border-t border-gray-200">
                                <button type="button" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" onclick="hideCreateDatabaseModal()">
                                    Cancel
                                </button>
                                <button type="submit" class="flex-1 inline-flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create Database
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <script>
                    // Modal functions
                    function showCreateDatabaseModal() {
                        const modal = document.getElementById('createDatabaseModal');
                        modal.classList.remove('hidden');
                        modal.classList.add('flex');
                        document.getElementById('dbName').focus();
                    }

                    function hideCreateDatabaseModal() {
                        const modal = document.getElementById('createDatabaseModal');
                        modal.classList.add('hidden');
                        modal.classList.remove('flex');
                        document.getElementById('createDatabaseForm').reset();
                    }

                    // Form submission
                    async function handleCreateDatabase(event) {
                        event.preventDefault();

                        const formData = new FormData(event.target);
                        const data = {
                            name: formData.get('name'),
                            language: formData.get('language'),
                            demo: formData.has('demo')
                        };

                        try {
                            const response = await fetch('/api/databases', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(data)
                            });

                            const result = await response.json();

                            if (response.ok &amp;&amp; result.success) {
                                hideCreateDatabaseModal();
                                alert('Database created successfully! Redirecting...');
                                window.location.href = '/home?db=' + encodeURIComponent(data.name);
                            } else {
                                throw new Error(result.detail || result.message || 'Failed to create database');
                            }
                        } catch (error) {
                            console.error('Error creating database:', error);
                            alert('Failed to create database: ' + error.message);
                        }
                    }

                    function connectToDatabase(dbName) {
                        // Add loading state to the clicked card
                        const card = document.querySelector(`[data-dbname="${dbName}"]`);
                        if (card) {
                            const button = card.querySelector('button');
                            if (button) {
                                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Connecting...';
                                button.disabled = true;
                                button.classList.add('opacity-75');
                            }
                        }

                        // Navigate to the database
                        window.location.href = '/home?db=' + encodeURIComponent(dbName);
                    }

                    function initializeDatabase(dbName) {
                        if (confirm('This will initialize the database with the base module. This process may take a few minutes. Continue?')) {
                            // Add loading state to the clicked card
                            const card = document.querySelector(`[data-dbname="${dbName}"]`);
                            if (card) {
                                const button = card.querySelector('button');
                                if (button) {
                                    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Initializing...';
                                    button.disabled = true;
                                    button.classList.add('opacity-75');
                                }
                            }

                            // For now, redirect to home which will trigger initialization through middleware
                            // In a full implementation, you might want to call a specific initialization endpoint
                            alert('Database initialization will begin when you connect. Please wait for the process to complete.');
                            window.location.href = '/home?db=' + encodeURIComponent(dbName);
                        }
                    }

                    async function deleteDatabase(dbName) {
                        if (confirm(`Are you sure you want to delete database "${dbName}"? This action cannot be undone.`)) {
                            const confirmText = prompt('This will permanently delete all data in the database. Type "DELETE" to confirm:');
                            if (confirmText === 'DELETE') {
                                try {
                                    const response = await fetch(`/api/databases/${encodeURIComponent(dbName)}`, {
                                        method: 'DELETE',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        }
                                    });

                                    const result = await response.json();

                                    if (response.ok &amp;&amp; result.success) {
                                        alert('Database deleted successfully');
                                        window.location.reload();
                                    } else {
                                        throw new Error(result.detail || result.message || 'Failed to delete database');
                                    }
                                } catch (error) {
                                    console.error('Error deleting database:', error);
                                    alert('Failed to delete database: ' + error.message);
                                }
                            } else if (confirmText !== null) {
                                alert('Deletion cancelled. You must type "DELETE" exactly to confirm.');
                            }
                        }
                    }

                    function refreshDatabases() {
                        window.location.reload();
                    }

                    // Search functionality
                    function filterDatabases() {
                        const searchTerm = document.getElementById('database-search').value.toLowerCase();
                        const cards = document.querySelectorAll('[data-dbname]');

                        cards.forEach(card => {
                            const dbName = card.dataset.dbname.toLowerCase();
                            const shouldShow = dbName.includes(searchTerm);
                            card.style.display = shouldShow ? '' : 'none';
                        });
                    }

                    // Initialize when page loads
                    document.addEventListener('DOMContentLoaded', function() {
                        // Handle modal close on backdrop click
                        const modal = document.getElementById('createDatabaseModal');
                        if (modal) {
                            modal.addEventListener('click', function(e) {
                                if (e.target === modal) {
                                    hideCreateDatabaseModal();
                                }
                            });
                        }

                        // Handle escape key to close modal
                        document.addEventListener('keydown', function(e) {
                            if (e.key === 'Escape') {
                                const modal = document.getElementById('createDatabaseModal');
                                if (modal &amp;&amp; !modal.classList.contains('hidden')) {
                                    hideCreateDatabaseModal();
                                }
                            }
                        });
                    });
                </script>
            </body>
        </html>
    </t>

</templates>
